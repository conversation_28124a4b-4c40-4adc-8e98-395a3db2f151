<?php

namespace App\Services\AIServices;

use Illuminate\Support\Facades\Log;

class GoogleService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('Google Gemini');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
        ];
    }

    // Fallback error Message Demo
    //     [2025-07-07 17:34:57] local.ERROR: AI Service Request Failed {"provider":"Google Gemini","endpoint":"/v1beta/models/gemma-3-12b-it:generateContent?key=AIzaSyDtEZn19RcY6tf2W3psqANHjypqHm2C4tE","status":503,"response":"{
    //   \"error\": {
    //     \"code\": 503,
    //     \"message\": \"The model is overloaded. Please try again later.\",
    //     \"status\": \"UNAVAILABLE\"
    //   }
    // }
    // "}

    public function chat(string $message, ?array $options = []): array
    {
        if (!isset($options['model'])) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $message
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature']??$this->getModelTemperature($options['model'], 'chat') ?? 0.7,
                'maxOutputTokens' => $options['max_tokens']??$this->getModelMaxTokens($options['model'], 'chat') ?? 1000,
                'topP' => $options['top_p'] ?? 0.8,
                'topK' => $options['top_k'] ?? 10,
            ]
        ];

        // Handle system message only if conversation history is not being processed
        if (!isset($options['conversation_history']) || empty($options['conversation_history'])) {
            $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
            if (isset($options['system_message'])) {
                $systemMessage = $systemMessage . "\n" . $options['system_message'];
            }

            // Only add systemInstruction for models that support it
            if ($systemMessage && $this->supportsSystemInstruction($options['model'])) {
                $data['systemInstruction'] = [
                    'parts' => [
                        [
                            'text' => $systemMessage
                        ]
                    ]
                ];
            } elseif ($systemMessage) {
                // For models that don't support systemInstruction, prepend system message to user message
                $data['contents'][0]['parts'][0]['text'] = $systemMessage . "\n\n" . $message;
            }
        }

        // Handle conversation history - Google format uses proper conversation structure
        if (isset($options['conversation_history']) && is_array($options['conversation_history']) && !empty($options['conversation_history'])) {
            // Build conversation history in Google's format
            $conversationContents = [];

            foreach ($options['conversation_history'] as $msg) {
                $role = ($msg['role'] === 'assistant' || $msg['role'] === 'model') ? 'model' : 'user';
                $conversationContents[] = [
                    'role' => $role,
                    'parts' => [
                        [
                            'text' => $msg['content']
                        ]
                    ]
                ];
            }

            // Add current user message
            $conversationContents[] = [
                'role' => 'user',
                'parts' => [
                    [
                        'text' => $message
                    ]
                ]
            ];

            // Replace the contents with full conversation
            $data['contents'] = $conversationContents;

            // Add system message if available
            $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
            if (isset($options['system_message'])) {
                $systemMessage = $systemMessage . "\n" . $options['system_message'];
            }

            if ($systemMessage && $this->supportsSystemInstruction($options['model'])) {
                $data['systemInstruction'] = [
                    'parts' => [
                        [
                            'text' => $systemMessage
                        ]
                    ]
                ];
            }
        }

        // Handle vision capabilities
        if (isset($options['images']) && is_array($options['images'])) {
            $parts = [
                [
                    'text' => $message
                ]
            ];

            foreach ($options['images'] as $image) {
                // Assuming base64 encoded images
                $parts[] = [
                    'inline_data' => [
                        'mime_type' => $options['image_mime_type'] ?? 'image/jpeg',
                        'data' => $image
                    ]
                ];
            }

            $data['contents'][count($data['contents']) - 1]['parts'] = $parts;
        }
        Log::info('🤖 Google Chat Request Data:', [
            'model' => $options['model'],
            'has_conversation_history' => isset($options['conversation_history']) && !empty($options['conversation_history']),
            'conversation_length' => isset($options['conversation_history']) ? count($options['conversation_history']) : 0,
            'contents_count' => count($data['contents']),
            'has_system_instruction' => isset($data['systemInstruction']),
            'data' => $data
        ]);

        $endpoint = "/v1beta/models/{$options['model']}:generateContent?key={$this->apiKey}";
        $response = $this->makeRequest($endpoint, $data);

        // Parse and validate Gemini response
        return $this->parseGeminiResponse($response);
    }

    /**
     * Parse Gemini response and extract text content
     */
    private function parseGeminiResponse(array $response): array
    {
        // Check for successful response with content
        if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'content' => $response['candidates'][0]['content']['parts'][0]['text'],
                'raw_response' => $response
            ];
        }

        // Check if response was truncated due to token limit
        if (
            isset($response['candidates'][0]['finishReason']) &&
            $response['candidates'][0]['finishReason'] === 'MAX_TOKENS'
        ) {
            Log::warning('Gemini response truncated due to MAX_TOKENS');
            return [
                'success' => false,
                'error' => 'MAX_TOKENS',
                'message' => 'عذراً، الإجابة طويلة جداً. يرجى طرح سؤال أكثر تحديداً.',
                'raw_response' => $response
            ];
        }

        // Check for other Gemini error conditions
        if (isset($response['candidates'][0]['finishReason'])) {
            $finishReason = $response['candidates'][0]['finishReason'];
            Log::warning('Gemini response finished with reason: ' . $finishReason);

            $errorMessages = [
                'SAFETY' => 'عذراً، لا أستطيع الإجابة على هذا السؤال لأسباب تتعلق بالسلامة.',
                'RECITATION' => 'عذراً، لا أستطيع تكرار محتوى محمي بحقوق الطبع والنشر.',
                'OTHER' => 'عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
            ];

            return [
                'success' => false,
                'error' => $finishReason,
                'message' => $errorMessages[$finishReason] ?? 'عذراً، حدث خطأ غير متوقع.',
                'raw_response' => $response
            ];
        }

        // No valid response found
        Log::error('Unexpected Gemini response structure:', ['response' => $response]);
        return [
            'success' => false,
            'error' => 'INVALID_RESPONSE',
            'message' => 'عذراً، لم أتمكن من فهم رسالتك. يرجى المحاولة مرة أخرى.',
            'raw_response' => $response
        ];
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => "Generate an image: " . $prompt
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? $this->getModelTemperature($model, 'image_generates') ?? 0.7,
                'maxOutputTokens' => $options['max_tokens'] ?? $this->getModelMaxTokens($model, 'image_generates') ?? 1000,
            ]
        ];

        // Add image generation specific parameters if available
        if (isset($dynamicOptions['size'])) {
            $data['generationConfig']['imageSize'] = $dynamicOptions['size'];
        }

        $endpoint = "/models/{$model}:generateContent?key={$this->apiKey}";
        return $this->makeRequest($endpoint, $data);
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => "Edit this image: " . $prompt
                        ],
                        [
                            'inline_data' => [
                                'mime_type' => $options['image_mime_type'] ?? 'image/jpeg',
                                'data' => $imageUrl // Assuming base64 encoded image
                            ]
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? $this->getModelTemperature($model, 'image_edits') ?? 0.7,
                'maxOutputTokens' => $options['max_tokens'] ?? $this->getModelMaxTokens($model, 'image_edits') ?? 1000,
            ]
        ];

        // Add image editing specific parameters if available
        if (isset($dynamicOptions['size'])) {
            $data['generationConfig']['imageSize'] = $dynamicOptions['size'];
        }

        $endpoint = "/models/{$model}:generateContent?key={$this->apiKey}";
        return $this->makeRequest($endpoint, $data);
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        // Google doesn't have video generation in Gemini yet
        throw new \Exception('Video generation not available for Google Gemini models');
    }

    public function streamChat(string $message, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $message
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? $this->getModelTemperature($model, 'chat') ?? 0.7,
                'maxOutputTokens' => $options['max_tokens'] ?? $this->getModelMaxTokens($model, 'chat') ?? 1000,
            ]
        ];

        $endpoint = "/v1beta/models/{$model}:streamGenerateContent?key={$this->apiKey}";
        return $this->makeRequest($endpoint, $data);
    }

    public function countTokens(string $text, ?string $model = null): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $text
                        ]
                    ]
                ]
            ]
        ];

        $endpoint = "/v1beta/models/{$model}:countTokens?key={$this->apiKey}";
        return $this->makeRequest($endpoint, $data);
    }

    /**
     * Check if a model supports system instructions (developer instructions)
     */
    private function supportsSystemInstruction(string $model): bool
    {
        // Models that support systemInstruction
        $supportedModels = [
            'gemini-1.5-pro',
            'gemini-1.5-pro-latest',
            'gemini-1.5-flash',
            'gemini-1.5-flash-latest',
            'gemini-1.0-pro',
            'gemini-pro',
            'gemini-2.0-flash-exp',
        ];

        // Check if the model name starts with any of the supported model names
        foreach ($supportedModels as $supportedModel) {
            if (str_starts_with($model, $supportedModel)) {
                return true;
            }
        }

        return false;
    }
}
