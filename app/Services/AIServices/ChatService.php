<?php

namespace App\Services\AIServices;

use App\Helpers\ClinicQueryHelper;
use App\Helpers\DoctorQueryHelper;
use App\Services\AIServices\AIServiceFactory;

class ChatService
{
    private $analyzer = [
        'provider' => 'google',
        'model' => 'gemini-2.5-flash-lite-preview-06-17',
        'max_tokens' => 100,
        'temperature' => 0.1,
    ];

    private $searcher = [
        'provider' => 'google',
        'model' => 'gemini-2.5-flash-lite-preview-06-17',
        'max_tokens' => 500,
        'temperature' => 0.2,
    ];

    private $responder = [
        'provider' => 'google',
        'model' => 'gemini-2.5-flash-lite-preview-06-17',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {
            // 1. تحليل الطلب
            $analysis = $this->analyzeRequest($userMessage);

            // 2. البحث في البيانات
            $searchResult = $this->searchBusinessData($analysis, $context);

            // 3. إنشاء الرد
            $response = $this->generateFinalResponse($userMessage, $searchResult, $context);

            return $response;
        } catch (\Exception $e) {
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * 1. تحليل الطلب
     */
    private function analyzeRequest(string $userMessage): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);

        $prompt = "حلل الطلب وحدد ماذا يريد العميل:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "أرجع واحد فقط من هذه الخيارات:\n";
        $prompt .= "- اسم_البزنس\n";
        $prompt .= "- كل_الاطباء\n";
        $prompt .= "- اطباء_تخصص\n";
        $prompt .= "- طبيب_معين\n";
        $prompt .= "- كل_العيادات\n";
        $prompt .= "- عيادة_معينة\n";
        $prompt .= "- حجز_موعد\n";
        $prompt .= "- محادثة_عامة\n";

        $response = $aiService->chat($prompt, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }

    /**
     * 2. البحث في البيانات
     */
    private function searchBusinessData(string $analysis, array $context): string
    {
        $businessData = $context['business_data'] ?? [];
        $userId = $context['user_id'] ?? null;

        if (!$userId || empty($businessData)) {
            return 'لا توجد بيانات متاحة';
        }

        $aiService = AIServiceFactory::create($this->searcher['provider']);

        $prompt = "أنت باحث ذكي. استخرج البيانات المطلوبة من بيانات البزنس:\n\n";
        $prompt .= "المطلوب: {$analysis}\n\n";
        $prompt .= "بيانات البزنس:\n" . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $prompt .= "أرجع البيانات المطلوبة فقط بشكل منظم ومختصر.";

        $response = $aiService->chat($prompt, $this->searcher);
        return $response['success'] ? $response['content'] : 'لم أجد البيانات المطلوبة';
    }

    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';

        $prompt = "أنت موظف في {$businessName}. رد على العميل بناءً على البيانات المتاحة:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "البيانات المتاحة:\n{$searchResult}\n\n";
        $prompt .= "اكتب رد مهذب ومفيد باللهجة السعودية. لا تتجاوز 200 كلمة.";

        $response = $aiService->chat($prompt, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
        ]);

        return $response['success'] ? $response['content'] : 'أعتذر، لا أستطيع المساعدة حالياً.';
    }



    /**
     * إنشاء الرد بناءً على التحليل
     */
    private function generateResponseFromAnalysis(string $message,array $analysis, array $context): string
    {
        $userId = $context['user_id'] ?? null;
        $businessName = $context['business_name'] ?? 'العيادة';

        // جلب البيانات المطلوبة بناءً على التحليل
        $data = $this->fetchRequiredData($analysis, $userId);

        // إنشاء الرد النهائي
        $aiService = AIServiceFactory::create($this->responder['provider']);
        $responsePrompt = $this->buildResponsePrompt($analysis, $data, $businessName);

        $response = $aiService->chat($message, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $responsePrompt,
        ]);

        if (isset($response['success']) && $response['success'] === true) {
            return $response['content'];
        }

        return "يامرحبا حياكم والمعذره كل الموظفين الأن مشغولين بنرد عليك بأقرب وقت ولاتقلق";
    }

    /**
     * بناء prompt لتحليل طلب العميل
     */
    private function buildAnalysisPrompt(string $userMessage): string
    {
        return <<<PROMPT
حلل رسالة العميل وأرجع JSON فقط:

رسالة العميل: "{$userMessage}"

أرجع JSON بهذا الشكل:
{
  "intent": "doctor_inquiry|clinic_inquiry|appointment_inquiry|general_inquiry",
  "entity_type": "doctor|clinic|appointment|none",
  "entity_name": "التخصص أو اسم الطبيب أو العيادة",
  "action": "search|info|help|book"
}

أمثلة:
- "من الدكتور حق الاسنان؟" => {"intent": "doctor_inquiry", "entity_type": "doctor", "entity_name": "الاسنان", "action": "search"}
- "تسوس" => {"intent": "doctor_inquiry", "entity_type": "doctor", "entity_name": "الاسنان", "action": "search"}
- "عيادة العيون" => {"intent": "clinic_inquiry", "entity_type": "clinic", "entity_name": "العيون", "action": "info"}
- "ابي احجز موعد" => {"intent": "appointment_inquiry", "entity_type": "appointment", "entity_name": "", "action": "book"}
- "سلام عليكم" => {"intent": "general_inquiry", "entity_type": "none", "entity_name": "", "action": "help"}

- "اريد موعد مع د.أحمد"
  => {"intent": "appointment_inquiry", "entity_type": "doctor", "entity_name": "أحمد", "action": "book"}

- "هل عندكم عيادة اسنان؟"
  => {"intent": "clinic_inquiry", "entity_type": "clinic", "entity_name": "الأسنان", "action": "info"}

- "مرحبا"
  => {"intent": "general_inquiry", "entity_type": "none", "entity_name": "", "action": "help"}

مهم:
- أرجع فقط JSON بدون أي نص آخر
- التزم بالتنسيق حرفيًا
- إذا كانت الرسالة غير مفهومة أو لا تحتوي معلومات كافية، استخدم:
  {"intent": "general_inquiry", "entity_type": "none", "entity_name": "", "action": "help"}

PROMPT;
    }

    /**
     * جلب البيانات المطلوبة بناءً على التحليل
     */
    private function fetchRequiredData(array $analysis, ?int $userId): array
    {
        if (!$userId) {
            return [];
        }

        $data = [];

        switch ($analysis['intent']) {
            case 'doctor_inquiry':
                $data = $this->fetchDoctorData($analysis, $userId);
                break;
            case 'clinic_inquiry':
                $data = $this->fetchClinicData($analysis, $userId);
                break;
            case 'appointment_inquiry':
                $data = $this->fetchAppointmentData($analysis, $userId);
                break;
            default:
                $data = [];
        }

        return $data;
    }

    /**
     * جلب بيانات الأطباء
     */
    private function fetchDoctorData(array $analysis, int $userId): array
    {
        $doctorHelper = new DoctorQueryHelper();

        if (!empty($analysis['entity_name'])) {
            // البحث عن طبيب معين أو تخصص
            $result = $doctorHelper->getDoctorsBySpecialization($analysis['entity_name'], $userId);
            if (!$result['found']) {
                $result = $doctorHelper->findDoctorByName($analysis['entity_name'], $userId);
            }
            return $result;
        }

        // إرجاع الأطباء المتاحين
        return $doctorHelper->getAvailableDoctors($userId, 5);
    }

    /**
     * جلب بيانات العيادات
     */
    private function fetchClinicData(array $analysis, int $userId): array
    {
        $clinicHelper = new ClinicQueryHelper();

        if (!empty($analysis['entity_name'])) {
            return $clinicHelper->findClinicByName($analysis['entity_name'], $userId);
        }

        return $clinicHelper->getOpenClinics($userId);
    }

    /**
     * جلب بيانات المواعيد
     */
    private function fetchAppointmentData(array $analysis, int $userId): array
    {
        // منطق بسيط للمواعيد
        return [
            'message' => 'يمكنك حجز موعد عبر الاتصال بنا أو زيارة العيادة'
        ];
    }

    /**
     * بناء prompt للرد النهائي
     */

private function buildResponsePrompt(array $analysis, array $data, string $businessName): string
{
    $prompt = "أنت موظف في {$businessName}.\n";
    $prompt .= "مهمتك الرد على استفسارات العملاء بأسلوب مهذب، ودود، ومفيد.\n";
    $prompt .= "- اكتب الرد باللغة العربية، باللهجة السعودية البسيطة.\n";
    $prompt .= "- خلك لبِق، واضح، ولا تتجاوز 200 كلمة.\n";
    $prompt .= "- لا تكرر المعلومات، وركّز على إفادة العميل بناءً على التحليل.\n";
    $prompt .= "- إذا ما كان فيه معلومات كافية، اعتذر بلُطف واقترح طريقة للمساعدة.\n\n";

    $prompt .= "تحليل رسالة العميل:\n" . json_encode($analysis, JSON_UNESCAPED_UNICODE) . "\n\n";

    if (!empty($data)) {
        $prompt .= "البيانات المتاحة (مثلاً أسماء أطباء، تخصصات، مواعيد):\n" . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
    } else {
        $prompt .= "لا يوجد بيانات إضافية حاليًا.\n\n";
    }

    $prompt .= "اكتب رد مناسب للعميل:";

    return $prompt;
}

}
